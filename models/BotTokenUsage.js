import {DataTypes} from 'sequelize';
import {sequelize} from '../db.js';

const BotTokenUsage = sequelize.define('BotTokenUsage', {
    id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
    },
    botId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
            model: 'bots',
            key: 'id'
        }
    },
    messageId: {
        type: DataTypes.UUID,
        allowNull: true
    },
    tokensUsed: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Number of tokens used in this message (multiplied by model price)'
    },
    modelTokensUsed: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Number of tokens used in this message'
    },
    model: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'The AI model used for this message'
    },
    usageDate: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
    }
}, {
    tableName: 'bot_token_usages',
    timestamps: true,
    indexes: [
        {fields: ['botId'], name: 'botId_index'},
        {fields: ['usageDate'], name: 'usageDate_index'}
    ]
});

export default BotTokenUsage;
