// Test file to check if DialogAnalyzer can be imported
import DialogAnalyzerTool from './tools/dialog_analyzer.js';

console.log('DialogAnalyzer imported successfully');
console.log('Class name:', DialogAnalyzerTool.name);

// Test basic instantiation
try {
    const analyzer = new DialogAnalyzerTool({});
    console.log('DialogAnalyzer instantiated successfully');
    console.log('Default config:', analyzer.getDefaultConfig());
    console.log('Cron settings:', analyzer.getCronSettings());
    console.log('Menu items count:', analyzer.getMenuItems().length);
    console.log('Registered events:', analyzer.getRegisteredEvents());
} catch (error) {
    console.error('Error instantiating DialogAnalyzer:', error.message);
}
