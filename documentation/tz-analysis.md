# Техническое задание: Инструмент DialogAnalyzer

## 1\. Назначение

Инструмент для автоматического анализа завершенных диалогов с помощью ИИ и отправки результатов во внешние CRM-системы через REST API.

## 2\. Основные функции

*   Анализ диалогов после периода неактивности*   Сохранение результатов анализа в базе данных*   Отправка результатов во внешние системы через модульные интеграции*   Повторный анализ диалогов при появлении новых сообщений

## 3\. Архитектурные компоненты

### 3.1. Основной класс DialogAnalyzer

*   Наследуется от ToolInterface*   Реализует логику анализа диалогов*   Управляет настройками и интеграциями*   Запускается по расписанию через cron

### 3.2. Интерфейс интеграции (IntegrationInterface)

*   Базовый класс для всех интеграций с внешними системами*   Определяет стандартные методы для всех интеграций:
    *   send(data) - отправка данных*   update(externalId, data) - обновление данных*   delete(externalId) - удаление данных*   find(query) - поиск данных

### 3.3. Модули интеграций

*   Отдельные классы для каждой CRM-системы*   Наследуются от IntegrationInterface*   Реализуют специфичную логику для конкретной CRM

## 4\. Настройки инструмента

*   Промпт для анализа диалога*   Модель ИИ для анализа (например, gpt-4o)*   Таймаут неактивности (в секундах)*   Флаг повторного анализа при новых сообщениях*   Тип интеграции (например, 'bitrix24', 'amocrm')*   Настройки для выбранной интеграции

## 5\. Хранение данных

*   Настройки инструмента хранятся в storage бота*   Данные о последнем анализе хранятся в storage пользователя:
    *   Время последнего анализа*   ID последнего проанализированного сообщения*   Внешний ID в CRM-системе*   Маппинг между внутренними ID и внешними ID хранится в storage пользователя

## 6\. Процесс работы

*   Инструмент запускается по расписанию (каждые 5 минут)*   Находит диалоги, неактивные дольше заданного таймаута*   Для каждого диалога:
    *   Проверяет, был ли диалог уже проанализирован*   Если диалог не анализировался или появились новые сообщения (и включен повторный анализ), выполняет анализ*   Формирует запрос к OpenAI с заданным промптом и историей сообщений*   Сохраняет результат анализа в storage пользователя*   Отправляет результат во внешнюю систему через соответствующую интеграцию*   Сохраняет внешний ID для будущих обновлений

## 8\. Интеграции с внешними системами

*   Каждая интеграция реализуется как отдельный модуль*   Модули размещаются в директории `/integrations/`*   Минимальный набор интеграций:
    *   Bitrix24*   AmoCRM*   Общий REST API (для произвольных систем)

## 9\. Безопасность

*   Учетные данные для внешних систем хранятся в storage пользователя*   Доступ к настройкам инструмента имеет только владелец бота

## 10\. Пользовательский интерфейс (только владельцу бота)

*   Настройка инструмента через меню. Владелец может:
    *   Установить промпт для анализа диалога
    *   Выбрать модель ИИ для анализа
    *   Установить таймаут неактивности
    *   Включить/выключить повторный анализ при новых сообщениях
    *   Выбрать тип интеграции (например, 'bitrix24', 'amocrm')
    *   Настроить параметры для выбранной интеграции
*   Возможность запустить тестовый анализ любого диалога бота без отправки в CRM с получением результата в виде приложенного файла
*   Возможность запустить тестовый анализ любого диалога бота с отправкой в CRM

## 11\. Технические требования

*   Язык реализации: JavaScript*   Совместимость с существующей архитектурой инструментов*   Минимальное влияние на производительность системы*   Корректная обработка ошибок и логирование

## 12\. Расширяемость

*   Возможность добавления новых типов интеграций без изменения основного кода*   Возможность расширения функциональности анализа в будущем

## 13\. Дополнительно
* Документацию писать не надо
* Тесты писать не надо
* getDefinitions и execute не надо
* никаких команд владелец писать не должен, все через меню