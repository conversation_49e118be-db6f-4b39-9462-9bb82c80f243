# Руководство разработчика инструментов

## Введение

Данное руководство предназначено для разработчиков, создающих инструменты (tools) для чат-ботов на базе OpenAI.
Инструменты могут реагировать на события системы и выполнять различные действия.

## Основной интерфейс

Все инструменты должны наследоваться от класса `ToolInterface`:

```javascript
import {ToolInterface} from './base.js';

class MyCustomTool extends ToolInterface {
    // Ваш код инструмента
}
```

## Методы инструментов

### getDefinition()

Возвращает определение инструмента для OpenAI в формате JSON. Либо массив инструментов, либо пустой объект. Может быть
пустым объектом, если инструмент не предназначен для использования через OpenAI:

```javascript
getDefinition()
{
    // Если инструмент не предназначен для OpenAI - возвращаем пустой объект
    return {};

    // Для инструментов, вызываемых через OpenAI:
    return {
        type: "function",
        function: {
            "name": "your_tool_name",
            "description": "Описание вашего инструмента",
            "strict": true,
            "parameters": {
                "type": "object",
                "properties": {
                    "param1": {
                        "type": "string",
                        "description": "Описание параметра"
                    }
                },
                "required": ["param1"]
            }
        }
    };
}
```

### execute(args)

Метод для выполнения действий инструмента при вызове через OpenAI:

```javascript
async
execute(args)
{
    try {
        // Действия инструмента при вызове через OpenAI
        return "Результат выполнения";
    } catch (e) {
        log.error('Ошибка', {msg: e.message}, 'execute', 'MyCustomTool');
        return `Ошибка: ${e.message}`;
    }
}
```

### getRegisteredEvents()

Возвращает список событий мессенджера, которые инструмент обрабатывает:

```javascript
getRegisteredEvents()
{
    return ['message_received', 'command_received', 'custom_event'];
}
```

### handleEvent(eventName, eventData, context)

Обрабатывает события мессенджера:

```javascript
async
handleEvent(eventName, eventData, context)
{
    if (eventName === 'command_received') {
        // Обработка команды
        return true; // Обработали событие, остановить дальнейшую обработку
    }
    return false; // Не обработали событие, передать управление следующему инструменту
}
```

### getApiEndpoints()

Возвращает список API-эндпоинтов, которые должны быть зарегистрированы для инструмента:

```javascript
getApiEndpoints()
{
    return [
        {
            path: '/webhook',
            method: 'POST',
            handler: this.handleWebhook.bind(this),
            middleware: [], // Опционально, массив middleware-функций
            description: 'Обработчик вебхуков от внешней системы'
        }
    ];
}
```

## Объект контекста

Контекст предоставляет инструменту доступ к данным и функциональности системы. Содержимое контекста может различаться в
зависимости от типа события и от места вызова инструмента.

### Основные компоненты контекста

В большинстве случаев контекст содержит следующие ключевые компоненты:

- `client` - клиент мессенджера с методами для отправки сообщений
- `bot` - данные о боте из базы данных
- `user` - данные о пользователе (ExternalUser)
- `chatId` - ID чата в мессенджере
- `botStorage` - хранилище данных бота
- `userStorage` - хранилище данных пользователя
- `oai` - API OpenAI для работы с моделями
- `isBotOwner` - флаг, указывающий, является ли пользователь владельцем бота
- `caller` - источник вызова ('telegram', 'OpenAI', и т.д.)

В зависимости от типа события могут быть доступны дополнительные поля:

### Контекст при вызове через OpenAI (метод execute)

```javascript
{
   // Данные, связанные с запросом OpenAI
   data: {
      id: String, // ID запуска
              thread_id
   :
      String, // ID треда
              status
   :
      String, // Статус запуска (completed, in_progress, etc)
              required_action
   :
      { ...
      } // Данные о требуемом действии
   }
,

   answer: {
      id: String, // ID записи в истории
              botId
   :
      String, // ID бота
              externalUserId
   :
      String, // ID внешнего пользователя
              message
   :
      String, // Текст сообщения
              direction
   :
      String, // Направление (incoming, outgoing)
              thread
   :
      String, // ID треда OpenAI
              runId
   :
      String, // ID запуска
              aiTokenCount
   :
      Object, // Счетчики токенов
              telegramMessageId
   :
      Number, // ID сообщения в Телеграм (если есть)
              err
   :
      Boolean, // Флаг ошибки
              errMessage
   :
      String // Сообщение ошибки (если есть)
   }
,

   threadId: String, // ID треда OpenAI

           // Данные о боте
           bot
:
   {
      id: String, // ID бота
              title
   :
      String, // Название бота
              user_id
   :
      String, // ID владельца
              tgToken
   :
      String, // Токен Телеграм
              tgName
   :
      String, // Имя в Телеграме
              assistantId
   :
      String, // ID ассистента OpenAI
              temperature
   :
      Number, // Температура генерации
              topP
   :
      Number, // Параметр top_p
              systemPrompt
   :
      String, // Системный промпт
              enabled
   :
      Boolean, // Активен ли бот
              externalUserId
   :
      String, // ID внешнего пользователя-владельца
              modelId
   :
      Number, // ID модели
              vectorID
   :
      String, // ID векторного хранилища
              storage
   :
      Object // Хранилище данных бота
   }
,

   history: {
      // Свойства аналогичны объекту answer
   }
,

   // API OpenAI
   oai: {
      createMessage: Function, // Создать сообщение
              startRun
   :
      Function, // Запустить выполнение
              voice2text
   :
      Function // Преобразование голоса в текст
      // ...другие методы API
   }
,

   caller: 'OpenAI', // Источник вызова

           // Данные пользователя (ExternalUser)
           user
:
   {
      id: String, // ID пользователя в системе
              user_id
   :
      String, // ID владельца бота
              botId
   :
      String, // ID бота
              externalId
   :
      String, // ID пользователя в мессенджере
              firstName
   :
      String, // Имя
              lastName
   :
      String, // Фамилия
              username
   :
      String, // Имя пользователя
              languageCode
   :
      String, // Код языка
              isBot
   :
      Boolean, // Бот ли пользователь
              conversationLanguage
   :
      String, // Язык общения
              storage
   :
      Object // Хранилище данных пользователя
   }
,

   isBotOwner: Boolean // Является ли пользователь владельцем бота
}
```

### Контекст при событиях мессенджера (метод handleEvent)

```javascript
{
    // Клиент мессенджера
    client: {
        // Основные методы TelegramClient
       sendMessage: async (chatId, text, options) => {
          // Отправить сообщение
       },
               sendTyping
    :
       async (chatId) => {
          // Отправить "печатает"
       },
               sendPhoto
    :
       async (chatId, photo, options) => {
          // Отправить фото
       },
               sendDocument
    :
       async (chatId, document, options) => {
          // Отправить документ
       },
               editMessage
    :
       async (chatId, messageId, text) => {
          // Редактировать сообщение
       },
               formatMessage
    :
       (text) => {
          // Форматировать текст для мессенджера
       },
               // ...другие методы клиента

               bot
    :
       Object, // Данные бота в Телеграме
               platform
    :
       String // Платформа (telegram)
    }
,

   chatId: Number, // ID чата в мессенджере

           // Исходное сообщение от мессенджера
           message
:
   {
        // Для Telegram
      message_id: Number, // ID сообщения
              from
   :
      {
         id: Number, // ID пользователя
                 first_name
      :
         String, // Имя
                 last_name
      :
         String, // Фамилия
                 username
      :
         String, // Имя пользователя
                 language_code
      :
         String // Код языка
      }
   ,
      chat: {
         id: Number, // ID чата
                 type
      :
         String, // Тип чата (private, group, etc)
                 first_name
      :
         String, // Имя
                 last_name
      :
         String, // Фамилия
                 username
      :
         String // Имя пользователя
      }
   ,
      date: Number, // Дата отправки (UNIX-время)
              text
   :
      String, // Текст сообщения (если есть)

              // Возможные дополнительные поля:
              photo
   :
      Array, // Фото (если есть)
              document
   :
      Object, // Документ (если есть)
              voice
   :
      Object // Голосовое сообщение (если есть)
        // ...другие поля
   }
,

    // Данные о боте из БД
    bot: {
        // Аналогично боту в контексте OpenAI
    }
,

    // Данные владельца бота
    owner: {
       id: String, // ID пользователя
               email
    :
       String, // Email
               password
    :
       String, // Хэш пароля
               openAIKey
    :
       String, // Ключ API OpenAI
               role
    :
       String, // Роль (admin, user, etc)
               language
    :
       String // Язык интерфейса
        // ...другие поля
    }
,

    // API OpenAI (аналогично как в контексте OpenAI)
    oai: {
        // Методы API
    }
,

    // Хранилища данных
    botStorage: {
       get: async (key, defaultValue) => {
          // Получить значение
       },
               set
    :
       async (key, value) => {
          // Установить значение
       },
               del
    :
       async (key) => {
          // Удалить значение
       },
               has
    :
       async (key) => {
          // Проверить наличие ключа
       },
               list
    :
       async (path) => {
          // Получить список ключей
       },
               clear
    :
       async () => {
          // Очистить хранилище
       }
    }
,

    userStorage: {
        // Аналогичные методы как у botStorage
    }
,

    // Данные пользователя
    user: {
        // Аналогично пользователю в контексте OpenAI
    }
,

   isBotOwner: Boolean, // Является ли пользователь владельцем бота

           // Источник события
           caller
:
   'telegram' // Платформа, вызвавшая событие
}
```

## Управление обработкой событий

Возвращаемое значение метода `handleEvent` критически важно:

- `true` - Событие полностью обработано, дальнейшая обработка другими инструментами **не будет выполняться**
- `false` - Событие не обработано или обработано частично, система продолжит обработку другими инструментами

Это позволяет создавать цепочки обработчиков или полностью перехватывать события.

## События

Система поддерживает следующие события мессенджера:

### Основные события

- `message_received` - получено текстовое сообщение от пользователя
- `command_received` - получена команда (сообщение с "/" или "!")
- `document_received` - получен документ от пользователя
- `photo_received` - получено фото от пользователя
- `voice_received` - получено голосовое сообщение от пользователя
- `answer_received` - получен ответ от OpenAI для отправки пользователю
- `sending_message` - вызывается перед отправкой сообщения от бота в мессенджер пользователю

### События меню и интерактивных элементов

- `menu_callback` - пользователь нажал на пункт меню, определенный в `getMenuItems()`
- `callback_query` - пользователь нажал на интерактивную кнопку, не связанную с меню

### Системные события

- `client_started` - вызывается при запуске клиента мессенджера
- `pre_checkout_query` - запрос на предварительную проверку платежа (для Telegram)
- `successful_payment` - успешный платеж (для Telegram)

### Пример обработки события `sending_message`

Это событие позволяет модифицировать сообщение перед отправкой пользователю:

```javascript
async
handleEvent(eventName, eventData, context)
{
   if (eventName === 'sending_message') {
      // Получаем текст и параметры сообщения
      let {text, form} = eventData;

      // Модифицируем текст (например, добавляем подпись)
      text = text + '\n\n_Отправлено через мой бот_';

      // Отправляем модифицированное сообщение
      const messageId = await context.client.client.sendMessage(context.chatId, text, form);

      // Сохраняем результат в контексте
      context.result = messageId;

      // Возвращаем true, чтобы предотвратить стандартную отправку
      return true;
   }
   return false;
}
```

## Работа с хранилищем

Пример использования хранилища:

```javascript
// Получение значения
const value = await userStorage.get('key', 'defaultValue');

// Установка значения
await userStorage.set('key', 'value');

// Удаление значения
await userStorage.del('key');

// Проверка наличия ключа
const exists = await userStorage.has('key');

// Поддерживаются вложенные ключи
await userStorage.set('parent.child', 'value');
```

## Система меню

Инструменты могут добавлять пункты меню в интерфейс бота. Для этого используется метод `getMenuItems()`:

```javascript
getMenuItems()
{
   return [
      {
         id: 'unique_item_id',         // Уникальный идентификатор пункта меню
         text: 'Текст пункта меню',    // Отображаемый текст
         callback_data: 'menu_action', // Данные для обработки при клике (макс. 64 байта)
         position: 'main',             // Позиция в меню (main, settings, custom_position, etc)
         permissions: ['owner', 'all'], // Кто может видеть этот пункт (owner - только владелец, all - все)
         order: 10                     // Порядок отображения (меньшие числа - выше)
      }
   ];
}
```

### Обработка событий меню

Когда пользователь нажимает на пункт меню, генерируется событие `menu_callback`. Для его обработки инструмент должен:

1. Зарегистрировать обработчик события `menu_callback` в методе `getRegisteredEvents()`
2. Обработать событие в методе `handleEvent()`

```javascript
getRegisteredEvents()
{
   return ['menu_callback', 'command_received'];
}

async
handleEvent(eventName, eventData, context)
{
   // Обработка событий меню
   if (eventName === 'menu_callback') {
      const {menuItem, query, action} = eventData;

      // Обработка разных пунктов меню по callback_data
      switch (menuItem.callback_data) {
         case 'menu_action':
            await this.handleMenuAction(context);
            return true;
         default:
            return false;
      }
   }

   return false;
}
```

### Структура меню

Меню организовано по позициям (position). Основные позиции:

- `main` - главное меню, доступное через команду /menu
- Пользовательские позиции - могут быть созданы инструментами для подменю

Когда пользователь выбирает пункт меню, система автоматически ищет все пункты меню с соответствующей позицией и
отображает их.

### Пример создания подменю

```javascript
getMenuItems()
{
   return [
      // Пункт в главном меню
      {
         id: 'my_tool_menu',
         text: '🔧 Мой инструмент',
         callback_data: 'my_tool_menu',
         position: 'main',
         permissions: ['all'],
         order: 20
      },
      // Пункты в подменю
      {
         id: 'my_tool_action1',
         text: '▶️ Действие 1',
         callback_data: 'my_tool_action1',
         position: 'my_tool', // Имя позиции для подменю
         permissions: ['all'],
         order: 10
      },
      {
         id: 'my_tool_action2',
         text: '⚙️ Действие 2',
         callback_data: 'my_tool_action2',
         position: 'my_tool', // Та же позиция для группировки
         permissions: ['owner'], // Только для владельца
         order: 20
      }
   ];
}
```

### Отображение меню

Для отображения меню используйте метод `sendMenu` клиента мессенджера:

```javascript
// Получение пунктов меню для определенной позиции
const menuItems = await context.client.getMenuItems('my_tool', ['all']);

// Отправка меню пользователю
const menuTitle = "Выберите действие:";
await context.client.sendMenu(context.chatId, menuTitle, menuItems);
```

## Многоязычность и переводы

Система поддерживает многоязычность с помощью функций `l` и `lp` из модуля `library/translation.js`. Эти функции
автоматически переводят текст на язык пользователя.

### Функция l() - простой перевод

```javascript
import {l} from '../library/translation.js';

// Перевод текста на язык пользователя
const translatedText = await l(user, 'Hello, how can I help you?');

// Отправка переведенного сообщения
await context.client.sendMessage(context.chatId, translatedText);

// Можно использовать непосредственно в вызове метода
await context.client.sendMessage(context.chatId, await l(user, 'Welcome to our bot!'));
```

### Функция lp() - перевод с поддержкой множественного числа

Функция `lp` используется для перевода текста с учетом множественного числа. Она принимает шаблон с плейсхолдером
`{count}` и число:

```javascript
import {lp} from '../library/translation.js';

// Перевод с учетом множественного числа
const count = 5;
const translatedText = await lp(user, 'You have {count} message', count);
// На русском: "У вас 5 сообщений"

// Еще пример
const fileCount = 1;
const message = await lp(user, 'You have {count} file', fileCount);
// На русском: "У вас 1 файл"
```

### Важные правила использования функций перевода

1. **Не используйте переменные внутри строк для перевода**. Это приведет к генерации новых переводов при каждом
   изменении переменной.

   Неправильно:
   ```javascript
   const name = "John";
   await l(user, `Hello, ${name}!`); // Неправильно - переменная внутри строки
   ```

   Правильно:
   ```javascript
   const name = "John";
   const greeting = await l(user, 'Hello, {name}!'); // Переводим шаблон
   const result = greeting.replace('{name}', name); // Затем подставляем значение
   ```

2. **Используйте статические строки** для перевода, чтобы они могли кэшироваться и сохраняться в базе данных.

3. **Для множественного числа всегда используйте lp()** вместо ручного формирования фраз с числами.

### Пример использования в инструменте

```javascript
import {ToolInterface} from './base.js';
import {l, lp} from '../library/translation.js';
import {log} from '../library/log.js';

class MultilingualTool extends ToolInterface {
   async handleEvent(eventName, eventData, context) {
      if (eventName === 'command_received' && eventData.command === 'stats') {
         const {client, chatId, user} = context;

         // Получаем данные
         const messageCount = 42;
         const fileCount = 7;

         // Формируем сообщение с переводом
         const title = await l(user, 'Your Statistics');
         const messagesText = await lp(user, 'You have {count} message', messageCount);
         const filesText = await lp(user, 'You have {count} file', fileCount);

         // Отправляем сообщение
         const message = `${title}:\n\n${messagesText}\n${filesText}`;
         await client.sendMessage(chatId, message);
         return true;
      }
      return false;
   }
}
```

## Логирование

Используйте встроенную систему логирования:

```javascript
import {log} from '../library/log.js';

log.info('Информационное сообщение', {data: 'data'}, 'methodName', 'ClassName');
log.error('Ошибка', {error: error.message}, 'methodName', 'ClassName');
log.debug('Отладочная информация', {debug: 'data'}, 'methodName', 'ClassName');
```

## Заключение

Инструменты предоставляют мощный способ расширения функциональности ботов. При разработке инструментов рекомендуется:

1. **Следовать принципу единой ответственности** - каждый инструмент должен решать одну конкретную задачу.

2. **Обрабатывать все возможные ошибки** - использовать try-catch и логировать все ошибки.

3. **Использовать многоязычность** - все тексты для пользователя должны проходить через функции `l` и `lp`.

4. **Проверять права доступа** - использовать `isBotOwner` для ограничения доступа к чувствительным функциям.

5. **Использовать хранилище для сохранения состояния** - использовать `botStorage` и `userStorage` для хранения настроек
   и состояния.

6. **Создавать интуитивный интерфейс** - использовать систему меню для удобного взаимодействия с пользователем.

Примеры инструментов можно найти в папке `tools/`, в частности:

- `menu_demo.js` - пример работы с меню
- `prompt_wizard.js` - мастер создания промптов
- `bot_manager.js` - управление ботами
- `workout_prompt_customization.js` - пример инструмента с многошаговым взаимодействием
