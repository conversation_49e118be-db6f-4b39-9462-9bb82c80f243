/**
 * Base interface for all CRM integrations
 */
class IntegrationInterface {
    /**
     * @param {Object} config - Integration configuration
     */
    constructor(config) {
        this.config = config;
        this.name = this.constructor.name;
    }

    /**
     * Send data to external system
     * @param {Object} data - Data to send
     * @returns {Promise<Object>} - Response with external ID
     */
    async send(data) {
        throw new Error('Integration must implement send method');
    }

    /**
     * Update data in external system
     * @param {string} externalId - External ID of the record
     * @param {Object} data - Data to update
     * @returns {Promise<Object>} - Response
     */
    async update(externalId, data) {
        throw new Error('Integration must implement update method');
    }

    /**
     * Delete data from external system
     * @param {string} externalId - External ID of the record
     * @returns {Promise<Object>} - Response
     */
    async delete(externalId) {
        throw new Error('Integration must implement delete method');
    }

    /**
     * Find data in external system
     * @param {Object} query - Search query
     * @returns {Promise<Object>} - Response
     */
    async find(query) {
        throw new Error('Integration must implement find method');
    }

    /**
     * Test connection to external system
     * @returns {Promise<boolean>} - Connection status
     */
    async testConnection() {
        throw new Error('Integration must implement testConnection method');
    }

    /**
     * Get configuration schema for this integration
     * @returns {Object} - Configuration schema
     */
    getConfigSchema() {
        throw new Error('Integration must implement getConfigSchema method');
    }

    /**
     * Validate configuration
     * @param {Object} config - Configuration to validate
     * @returns {Object} - Validation result {valid: boolean, errors: string[]}
     */
    validateConfig(config) {
        throw new Error('Integration must implement validateConfig method');
    }
}

export default IntegrationInterface;
