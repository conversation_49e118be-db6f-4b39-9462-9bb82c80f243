import IntegrationInterface from './base.js';
import axios from 'axios';
import {log} from '../library/log.js';

/**
 * Bitrix24 CRM Integration
 */
class Bitrix24Integration extends IntegrationInterface {
    constructor(config) {
        super(config);
        this.baseUrl = config.webhook_url || config.domain;
        this.accessToken = config.access_token;
    }

    /**
     * Send data to Bitrix24 (create lead)
     */
    async send(data) {
        try {
            log.info('Sending data to Bitrix24', {data}, 'send', 'Bitrix24Integration');
            
            const leadData = this.formatDataForBitrix24(data);
            const response = await this.makeRequest('crm.lead.add', {
                fields: leadData
            });

            if (response.result) {
                return {
                    success: true,
                    externalId: response.result.toString(),
                    data: response
                };
            } else {
                throw new Error('Failed to create lead in Bitrix24');
            }
        } catch (error) {
            log.error('Error sending data to Bitrix24', {
                error: error.message,
                data
            }, 'send', 'Bitrix24Integration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Update data in Bitrix24
     */
    async update(externalId, data) {
        try {
            log.info('Updating data in Bitrix24', {externalId, data}, 'update', 'Bitrix24Integration');
            
            const leadData = this.formatDataForBitrix24(data);
            const response = await this.makeRequest('crm.lead.update', {
                id: externalId,
                fields: leadData
            });

            return {
                success: !!response.result,
                data: response
            };
        } catch (error) {
            log.error('Error updating data in Bitrix24', {
                error: error.message,
                externalId,
                data
            }, 'update', 'Bitrix24Integration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Delete data from Bitrix24
     */
    async delete(externalId) {
        try {
            log.info('Deleting data from Bitrix24', {externalId}, 'delete', 'Bitrix24Integration');
            
            const response = await this.makeRequest('crm.lead.delete', {
                id: externalId
            });

            return {
                success: !!response.result,
                data: response
            };
        } catch (error) {
            log.error('Error deleting data from Bitrix24', {
                error: error.message,
                externalId
            }, 'delete', 'Bitrix24Integration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Find data in Bitrix24
     */
    async find(query) {
        try {
            log.info('Finding data in Bitrix24', {query}, 'find', 'Bitrix24Integration');
            
            const response = await this.makeRequest('crm.lead.list', {
                filter: query,
                select: ['ID', 'TITLE', 'NAME', 'LAST_NAME', 'EMAIL', 'PHONE']
            });

            return {
                success: true,
                data: response.result || []
            };
        } catch (error) {
            log.error('Error finding data in Bitrix24', {
                error: error.message,
                query
            }, 'find', 'Bitrix24Integration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Test connection to Bitrix24
     */
    async testConnection() {
        try {
            const response = await this.makeRequest('crm.lead.list', {
                filter: {},
                select: ['ID'],
                start: 0
            });
            return !!response.result;
        } catch (error) {
            log.error('Bitrix24 connection test failed', {
                error: error.message
            }, 'testConnection', 'Bitrix24Integration');
            return false;
        }
    }

    /**
     * Get configuration schema
     */
    getConfigSchema() {
        return {
            webhook_url: {
                type: 'string',
                required: true,
                description: 'Bitrix24 webhook URL (e.g., https://your-domain.bitrix24.com/rest/1/webhook_key/)'
            }
        };
    }

    /**
     * Validate configuration
     */
    validateConfig(config) {
        const errors = [];
        
        if (!config.webhook_url) {
            errors.push('Webhook URL is required');
        } else if (!config.webhook_url.includes('bitrix24.com/rest/')) {
            errors.push('Invalid Bitrix24 webhook URL format');
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * Make API request to Bitrix24
     */
    async makeRequest(method, params = {}) {
        const url = `${this.baseUrl}${method}`;
        
        const response = await axios.post(url, params, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });

        if (response.data.error) {
            throw new Error(`Bitrix24 API Error: ${response.data.error_description || response.data.error}`);
        }

        return response.data;
    }

    /**
     * Format data for Bitrix24 lead
     */
    formatDataForBitrix24(data) {
        const leadData = {
            TITLE: data.title || 'Dialog Analysis Result',
            COMMENTS: data.analysis || data.content || '',
            SOURCE_ID: 'WEB',
            STATUS_ID: 'NEW'
        };

        // Add contact information if available
        if (data.contact) {
            if (data.contact.name) {
                const nameParts = data.contact.name.split(' ');
                leadData.NAME = nameParts[0];
                if (nameParts.length > 1) {
                    leadData.LAST_NAME = nameParts.slice(1).join(' ');
                }
            }
            if (data.contact.email) {
                leadData.EMAIL = [{ VALUE: data.contact.email, VALUE_TYPE: 'WORK' }];
            }
            if (data.contact.phone) {
                leadData.PHONE = [{ VALUE: data.contact.phone, VALUE_TYPE: 'WORK' }];
            }
        }

        // Add custom fields if configured
        if (data.customFields) {
            Object.assign(leadData, data.customFields);
        }

        return leadData;
    }
}

export default Bitrix24Integration;
