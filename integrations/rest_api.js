import IntegrationInterface from './base.js';
import axios from 'axios';
import {log} from '../library/log.js';

/**
 * Generic REST API Integration
 */
class RestApiIntegration extends IntegrationInterface {
    constructor(config) {
        super(config);
        this.baseUrl = config.base_url;
        this.headers = config.headers || {};
        this.authType = config.auth_type || 'none'; // none, bearer, basic, api_key
        this.authConfig = config.auth_config || {};
    }

    /**
     * Send data via REST API
     */
    async send(data) {
        try {
            log.info('Sending data via REST API', {data}, 'send', 'RestApiIntegration');
            
            const endpoint = this.config.endpoints?.create || '/create';
            const url = `${this.baseUrl}${endpoint}`;
            
            const requestData = this.formatData(data, 'create');
            const response = await this.makeRequest(url, 'POST', requestData);

            // Try to extract external ID from response
            let externalId = null;
            if (response.id) {
                externalId = response.id.toString();
            } else if (response.data?.id) {
                externalId = response.data.id.toString();
            } else if (this.config.response_id_field && response[this.config.response_id_field]) {
                externalId = response[this.config.response_id_field].toString();
            }

            return {
                success: true,
                externalId,
                data: response
            };
        } catch (error) {
            log.error('Error sending data via REST API', {
                error: error.message,
                data
            }, 'send', 'RestApiIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Update data via REST API
     */
    async update(externalId, data) {
        try {
            log.info('Updating data via REST API', {externalId, data}, 'update', 'RestApiIntegration');
            
            const endpoint = this.config.endpoints?.update || `/update/${externalId}`;
            const url = `${this.baseUrl}${endpoint.replace('{id}', externalId)}`;
            
            const requestData = this.formatData(data, 'update');
            const response = await this.makeRequest(url, 'PUT', requestData);

            return {
                success: true,
                data: response
            };
        } catch (error) {
            log.error('Error updating data via REST API', {
                error: error.message,
                externalId,
                data
            }, 'update', 'RestApiIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Delete data via REST API
     */
    async delete(externalId) {
        try {
            log.info('Deleting data via REST API', {externalId}, 'delete', 'RestApiIntegration');
            
            const endpoint = this.config.endpoints?.delete || `/delete/${externalId}`;
            const url = `${this.baseUrl}${endpoint.replace('{id}', externalId)}`;
            
            const response = await this.makeRequest(url, 'DELETE');

            return {
                success: true,
                data: response
            };
        } catch (error) {
            log.error('Error deleting data via REST API', {
                error: error.message,
                externalId
            }, 'delete', 'RestApiIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Find data via REST API
     */
    async find(query) {
        try {
            log.info('Finding data via REST API', {query}, 'find', 'RestApiIntegration');
            
            const endpoint = this.config.endpoints?.search || '/search';
            const url = `${this.baseUrl}${endpoint}`;
            
            const params = new URLSearchParams();
            Object.keys(query).forEach(key => {
                params.append(key, query[key]);
            });
            
            const response = await this.makeRequest(`${url}?${params.toString()}`, 'GET');

            return {
                success: true,
                data: Array.isArray(response) ? response : (response.data || [])
            };
        } catch (error) {
            log.error('Error finding data via REST API', {
                error: error.message,
                query
            }, 'find', 'RestApiIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Test connection to REST API
     */
    async testConnection() {
        try {
            const endpoint = this.config.endpoints?.health || this.config.endpoints?.test || '/health';
            const url = `${this.baseUrl}${endpoint}`;
            
            const response = await this.makeRequest(url, 'GET');
            return !!response;
        } catch (error) {
            log.error('REST API connection test failed', {
                error: error.message
            }, 'testConnection', 'RestApiIntegration');
            return false;
        }
    }

    /**
     * Get configuration schema
     */
    getConfigSchema() {
        return {
            base_url: {
                type: 'string',
                required: true,
                description: 'Base URL of the REST API'
            },
            auth_type: {
                type: 'string',
                required: false,
                description: 'Authentication type (none, bearer, basic, api_key)',
                default: 'none'
            },
            auth_config: {
                type: 'object',
                required: false,
                description: 'Authentication configuration'
            },
            headers: {
                type: 'object',
                required: false,
                description: 'Additional headers to send with requests'
            },
            endpoints: {
                type: 'object',
                required: false,
                description: 'Custom endpoint paths'
            },
            response_id_field: {
                type: 'string',
                required: false,
                description: 'Field name in response that contains the record ID'
            }
        };
    }

    /**
     * Validate configuration
     */
    validateConfig(config) {
        const errors = [];
        
        if (!config.base_url) {
            errors.push('Base URL is required');
        } else {
            try {
                new URL(config.base_url);
            } catch {
                errors.push('Invalid base URL format');
            }
        }

        if (config.auth_type && !['none', 'bearer', 'basic', 'api_key'].includes(config.auth_type)) {
            errors.push('Invalid auth_type. Must be one of: none, bearer, basic, api_key');
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * Make HTTP request with authentication
     */
    async makeRequest(url, method = 'GET', data = null) {
        const config = {
            method,
            url,
            headers: {
                'Content-Type': 'application/json',
                ...this.headers
            },
            timeout: 30000
        };

        // Add authentication
        this.addAuthentication(config);

        if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
            config.data = data;
        }

        const response = await axios(config);
        return response.data;
    }

    /**
     * Add authentication to request config
     */
    addAuthentication(config) {
        switch (this.authType) {
            case 'bearer':
                if (this.authConfig.token) {
                    config.headers.Authorization = `Bearer ${this.authConfig.token}`;
                }
                break;
            case 'basic':
                if (this.authConfig.username && this.authConfig.password) {
                    const credentials = Buffer.from(`${this.authConfig.username}:${this.authConfig.password}`).toString('base64');
                    config.headers.Authorization = `Basic ${credentials}`;
                }
                break;
            case 'api_key':
                if (this.authConfig.key && this.authConfig.header) {
                    config.headers[this.authConfig.header] = this.authConfig.key;
                }
                break;
        }
    }

    /**
     * Format data for API request
     */
    formatData(data, operation) {
        // Apply field mapping if configured
        if (this.config.field_mapping && this.config.field_mapping[operation]) {
            const mapping = this.config.field_mapping[operation];
            const mappedData = {};
            
            Object.keys(mapping).forEach(sourceField => {
                const targetField = mapping[sourceField];
                if (data[sourceField] !== undefined) {
                    mappedData[targetField] = data[sourceField];
                }
            });
            
            return mappedData;
        }
        
        return data;
    }
}

export default RestApiIntegration;
