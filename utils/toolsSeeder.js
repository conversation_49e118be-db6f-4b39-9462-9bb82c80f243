import {AvailableTools} from '../models/associations.js';
import {v4 as uuidv4} from 'uuid';
import {log} from '../library/log.js';

/**
 * Seeds the AvailableTools table with new tools
 */
export async function seedTools() {
    try {
        // Define the tools to seed
        const tools = [
            {
                id: uuidv4(),
                toolName: 'menu_demo',
                title: 'Menu Demo',
                description: 'Demonstrates the menu functionality in messaging clients',
                isSystem: false,
                isActive: true,
                weight: 100 // High priority to ensure menu items are loaded first
            },
            {
                id: uuidv4(),
                toolName: 'document_processor',
                title: 'Document Processor',
                description: 'Processes and stores documents received via Telegram',
                isSystem: false,
                isActive: true,
                weight: 30 // High priority for document handling
            },
            {
                id: uuidv4(),
                toolName: 'photo_processor',
                title: 'Photo Processor',
                description: 'Processes and stores photos received via Telegram',
                isSystem: false,
                isActive: true,
                weight: 30 // High priority for photo handling
            },
            {
                id: uuidv4(),
                toolName: 'get_current_datetime',
                title: 'Get Current Datetime',
                description: 'Returns the current datetime',
                isSystem: true,
                isActive: true,
                weight: 10 // Standard priority
            },
            {
                id: uuidv4(),
                toolName: 'get_current_user_details',
                title: 'Get Current User Details',
                description: 'Returns the details of the current user',
                isSystem: true,
                isActive: true,
                weight: 10 // Standard priority
            },
            {
                id: uuidv4(),
                toolName: 'update_conversation_language',
                title: 'Update Conversation Language',
                description: 'Updates the language of the current conversation',
                isSystem: true,
                isActive: true,
                weight: 30 // Higher priority for language handling
            },
            {
                id: uuidv4(),
                toolName: 'view_or_update_prompt',
                title: 'View or Update Prompt',
                description: 'View or update the bot\'s prompt text (for bot owner only)',
                isSystem: false,
                isActive: true,
                weight: 40 // High priority for prompt management
            },
            {
                id: uuidv4(),
                toolName: 'clear_thread',
                title: 'Clear Thread',
                description: 'Clears the conversation thread and starts a fresh one',
                isSystem: true,
                isActive: true,
                weight: 50 // High priority - should be processed first
            },
            {
                id: uuidv4(),
                toolName: 'create_bot',
                title: 'Create Bot',
                description: 'Creates a new bot with the specified Telegram token for the current user',
                isSystem: false,
                isActive: true,
                weight: 10 // Standard priority
            },
            {
                id: uuidv4(),
                toolName: 'amo_crm',
                title: 'AmoCRM Integration',
                description: 'Integrates with AmoCRM to manage contacts and messages',
                isSystem: false,
                isActive: true,
                weight: 5 // Lower priority, should be run after other tools
            },
            {
                id: uuidv4(),
                toolName: 'test_tool_handler',
                title: 'Test Tool Handler',
                description: 'Allows developers to test custom tools by dynamically loading them from uploaded files',
                isSystem: false,
                isActive: true,
                weight: 15 // Slightly higher than standard
            },
            {
                id: uuidv4(),
                toolName: 'daily_reminder',
                title: 'Daily Reminder',
                description: 'Sends a daily reminder to the user',
                isSystem: false,
                isActive: true,
                weight: 10 // Standard priority
            },
            {
                id: uuidv4(),
                toolName: 'prompt_wizard',
                title: 'Prompt Wizard',
                description: 'Interactive wizard for creating custom prompts',
                isSystem: false,
                isActive: true,
                weight: 50 // High priority - needs to intercept messages
            },
            {
                id: uuidv4(),
                toolName: 'language_selector',
                title: 'Language Selector',
                description: 'Interactive wizard for selecting the conversation language',
                isSystem: true,
                isActive: true,
                weight: 500 // High priority - needs to intercept messages
            },
            {
                id: uuidv4(),
                toolName: 'subscription_handler',
                title: 'Paid Bots',
                description: 'Handles subscription payments for paid bots',
                isSystem: false,
                isActive: true,
                weight: 10 // Standard priority
            },
            {
                id: uuidv4(),
                toolName: 'v_keeper',
                title: 'Vicekeeper HR manager',
                description: 'Vicekeeper HR manager bot',
                isSystem: false,
                isActive: true,
                weight: 50
            },
            {
                id: uuidv4(),
                toolName: 'restart',
                title: 'Restart Bot',
                description: 'Restarts the bot instance',
                isSystem: false,
                isActive: true,
                weight: 50
            },
            {
                id: uuidv4(),
                toolName: 'bot_manager',
                title: 'Bot Manager',
                description: 'Manages bot settings and configurations',
                isSystem: false,
                isActive: true,
                weight: 50
            },
            {
                id: uuidv4(),
                toolName: 'token_tracking',
                title: 'Token Tracking',
                description: 'Tracks token usage for each bot and manages user token balances',
                isSystem: false,
                isActive: false,
                weight: 1000 // Very high priority to ensure it runs before other tools
            },
            {
                id: uuidv4(),
                toolName: 'easy_token_tracking',
                title: 'Token Tracking',
                description: 'Tracks token usage for each bot and manages user token balances. External requests',
                isSystem: true,
                isActive: true,
                weight: 1000 // Very high priority to ensure it runs before other tools
            },
            {
                id: uuidv4(),
                toolName: 'referral',
                title: 'Referral Program',
                description: 'Handles referral program logic',
                isSystem: true,
                isActive: true,
                weight: 1000
            },
            {
                id: uuidv4(),
                toolName: 'dialog_analyzer',
                title: 'Dialog Analyzer',
                description: 'Analyzes completed dialogues with AI and sends results to CRM systems',
                isSystem: false,
                isActive: true,
                weight: 5 // Low priority, runs in background
            }

        ];

        // Check if tools already exist
        for (const tool of tools) {
            const existingTool = await AvailableTools.findOne({
                where: {
                    toolName: tool.toolName
                }
            });

            if (!existingTool) {
                await AvailableTools.create(tool);
                log.info(`Created tool: ${tool.toolName}`, {}, 'seedTools', 'toolsSeeder.js');
            } else {
                // Update the weight of existing tools if they don't have one
                if (existingTool.weight === undefined || existingTool.weight === null) {
                    existingTool.weight = tool.weight;
                    await existingTool.save();
                    log.info(`Updated weight for tool: ${tool.toolName}`, {weight: tool.weight}, 'seedTools', 'toolsSeeder.js');
                }
            }
        }

        log.info('Tools seeding completed', {}, 'seedTools', 'toolsSeeder.js');
        return true;
    } catch (error) {
        log.error('Error seeding tools', {
            error: error.message
        }, 'seedTools', 'toolsSeeder.js');
        return false;
    }
}

export default seedTools;